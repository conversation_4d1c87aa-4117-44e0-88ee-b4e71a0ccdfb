package cmd

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

var (
	configFile string
)

// rootCmd 根命令
var rootCmd = &cobra.Command{
	Use:   "dbtool",
	Short: "数据库工具",
	Long: `一个强大的数据库命令行工具，支持：
- 数据库连接测试
- TUI查询界面
- 多种数据库类型支持`,
}

// Execute 执行根命令
func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "错误: %v\n", err)
		os.Exit(1)
	}
}

func init() {
	// 添加全局标志
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "config.yaml", "配置文件路径")
}
