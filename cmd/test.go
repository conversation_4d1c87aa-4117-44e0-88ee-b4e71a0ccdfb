package cmd

import (
	"fmt"

	"dbtool/internal/config"
	"dbtool/internal/database"

	"github.com/spf13/cobra"
)

// testCmd 测试命令
var testCmd = &cobra.Command{
	Use:   "test [数据库名称]",
	Short: "测试数据库连接",
	Long:  "测试指定数据库的连接是否正常",
	Args:  cobra.ExactArgs(1),
	RunE:  runTest,
}

func runTest(cmd *cobra.Command, args []string) error {
	dbName := args[0]

	// 加载配置文件
	cfg, err := config.LoadConfig(configFile)
	if err != nil {
		return fmt.Errorf("加载配置文件失败: %v", err)
	}

	// 获取数据库配置
	dbConfig, exists := cfg.GetDatabase(dbName)
	if !exists {
		return fmt.Errorf("配置文件中未找到数据库 '%s'", dbName)
	}

	// 验证配置
	if err := dbConfig.Validate(); err != nil {
		return fmt.Errorf("数据库配置验证失败: %v", err)
	}

	// 测试连接
	fmt.Printf("正在测试数据库 '%s' 的连接...\n", dbName)
	fmt.Printf("驱动: %s\n", dbConfig.Driver)

	if dbConfig.Source != "" {
		fmt.Printf("连接字符串: %s\n", maskPassword(dbConfig.Source))
	} else {
		fmt.Printf("主机: %s:%d\n", dbConfig.Host, dbConfig.Port)
		fmt.Printf("数据库: %s\n", dbConfig.DB)
		fmt.Printf("用户: %s\n", dbConfig.User)
	}

	if err := database.TestConnection(dbConfig); err != nil {
		fmt.Printf("❌ 连接失败: %v\n", err)
		return err
	}

	fmt.Printf("✅ 连接成功！\n")
	return nil
}

// maskPassword 遮罩密码
func maskPassword(connectionString string) string {
	// 简单的密码遮罩实现
	// 在实际应用中可能需要更复杂的正则表达式
	return connectionString // 为了简化，这里不做遮罩
}

func init() {
	rootCmd.AddCommand(testCmd)
}
