package cmd

import (
	"fmt"

	"dbtool/internal/config"
	"dbtool/internal/tui"

	"github.com/spf13/cobra"
)

// queryCmd 查询命令
var queryCmd = &cobra.Command{
	Use:   "query",
	Short: "打开TUI查询界面",
	Long:  "使用Charm Bubbles打开一个现代化的交互式TUI界面用于数据库查询",
	RunE:  runQuery,
}

func runQuery(cmd *cobra.Command, args []string) error {
	// 加载配置文件
	cfg, err := config.LoadConfig(configFile)
	if err != nil {
		return fmt.Errorf("加载配置文件失败: %v", err)
	}

	// 检查是否有数据库配置
	if len(cfg.Databases) == 0 {
		return fmt.Errorf("配置文件中未找到任何数据库配置")
	}

	// 创建并运行Charm Bubbles TUI
	queryTUI := tui.NewBubblesQueryTUI(cfg)
	if err := queryTUI.Run(); err != nil {
		return fmt.Errorf("运行TUI界面失败: %v", err)
	}

	return nil
}

func init() {
	rootCmd.AddCommand(queryCmd)
}
