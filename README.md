# DBTool - 数据库命令行工具

一个强大的数据库命令行工具，支持数据库连接测试和TUI查询界面。

## 功能特性

- 🔗 数据库连接测试
- 📊 基于Charm Bubbles的现代化TUI查询界面  
- 🗄️ 支持MySQL数据库
- ⚙️ 灵活的配置文件格式
- 🚀 快速响应和易于使用
- ✨ 美观的界面样式和交互体验
- 🎯 智能焦点切换和键盘快捷键

## 安装

### 从源码编译

```bash
git clone <repository>
cd dbtool
go mod download
go build -o dbtool
```

### 直接下载

从 Releases 页面下载预编译的二进制文件。

## 配置

创建 `config.yaml` 配置文件：

```yaml
db1:
  driver: mysql
  source: 'root:123456@tcp(127.0.0.1:3306)/threebody-admin?charset=utf8mb4&parseTime=True&loc=Local'

db2:
  driver: mysql
  host: 127.0.0.1
  port: 3306
  db: threebody-admin
  user: root
  password: 123456
  charset: utf8mb4
```

## 使用方法

### 测试数据库连接

```bash
./dbtool test db1
```

### 打开TUI查询界面

```bash
./dbtool query
```

### TUI界面交互流程

**操作步骤：**
1. 启动后显示数据库选择界面
2. 使用 `↑/↓` 选择数据库，`Enter` 确认
3. 选择后界面切换到查询输入模式（覆盖数据库选择界面）
4. 使用 `Tab` 键在"数据库选择"和"查询输入"间切换焦点
5. 在查询输入框有焦点时，输入SQL语句并按 `Enter` 执行
6. 查询结果显示在原界面下方

**快捷键：**
- `Tab`: 切换焦点（数据库选择 ⇄ 查询输入）
- `↑/↓`: 选择数据库
- `Enter`: 选择数据库 / 执行查询（仅查询输入框焦点时）
- `Ctrl+C`: 退出程序

### 指定配置文件

```bash
./dbtool -c /path/to/config.yaml test db1
```

## 配置格式说明

支持两种配置格式：

### 1. 连接字符串格式（推荐）

```yaml
db1:
  driver: mysql
  source: 'root:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local'
```

### 2. 分段配置格式

```yaml
db2:
  driver: mysql
  host: 127.0.0.1
  port: 3306
  db: database_name
  user: username
  password: password
  charset: utf8mb4
```

## 支持的数据库

- ✅ MySQL
- 🔄 PostgreSQL (计划中)
- 🔄 SQLite (计划中)

## 开发

### 项目结构

```
dbtool/
├── cmd/              # CLI命令定义
├── internal/
│   ├── config/       # 配置文件处理
│   ├── database/     # 数据库驱动
│   └── tui/          # TUI界面
├── main.go
├── config.yaml
└── go.mod
```

### 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
