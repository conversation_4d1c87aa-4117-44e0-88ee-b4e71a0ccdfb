# DBTool Charm Bubbles 演示

## 概览

DBTool 现在使用 Charm Bubbles 库实现了现代化的TUI查询界面，提供更好的用户体验和界面美观度。

## 主要特性

### 🎨 现代化界面设计
- 使用 Charm Bubbles 和 Lipgloss 实现美观的TUI界面
- 圆角边框和色彩高亮
- 动态状态提示和错误显示

### ⌨️ 智能交互
- **Tab键切换**: 在SQL输入框和执行按钮之间智能切换焦点
- **快捷键支持**: Ctrl+R 快速执行，Ctrl+C 退出
- **数据库切换**: 上下箭头键快速切换多个数据库配置

### 📊 查询结果展示
- 表格形式展示查询结果
- 支持大量数据的分页浏览
- NULL值特殊显示
- 动态列宽调整

## 使用演示

### 1. 启动查询界面
```bash
./dbtool query
```

### 2. 界面布局

**步骤1: 数据库选择界面**
```
┌─────────────────────────────────────────────────────────┐
│ 🗄️  数据库查询工具                                         │
│                                                         │
│ 请选择数据库 (使用↑↓选择，Enter确认，Tab切换到查询输入):    │
│                                                         │
│ ▶ db1                                                  │
│   db2                                                  │
│                                                         │
│ 快捷键:                                                 │
│ • Tab: 切换焦点（数据库选择 ⇄ 查询输入）                  │
│ • ↑/↓: 选择数据库                                       │
│ • Enter: 选择数据库 / 执行查询（仅查询输入框焦点时）        │
│ • Ctrl+C: 退出                                         │
└─────────────────────────────────────────────────────────┘
```

**步骤2: 查询输入界面（覆盖数据库选择）**
```
┌─────────────────────────────────────────────────────────┐
│ 🗄️  数据库查询工具                                         │
│                                                         │
│ 当前数据库: db1                                          │
│                                                         │
│ SQL 查询语句 (Enter执行查询，Tab切换到数据库选择):          │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ SELECT * FROM users LIMIT 10;                      │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ ┌─ 查询结果 ─────────────────────────────────────────────┐ │
│ │ id │ name │ email                                    │ │
│ │ 1  │ John │ <EMAIL>                        │ │
│ │ 2  │ Jane │ <EMAIL>                        │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 查询成功，返回 2 行                                      │
└─────────────────────────────────────────────────────────┘
```

### 3. 交互操作流程

**初始状态 - 数据库选择**
1. **选择数据库**: 使用↑↓键选择数据库
2. **确认选择**: 按Enter确认选择
3. **切换到查询输入**: 按Tab键切换焦点到查询输入框

**查询输入状态**  
1. **输入SQL**: 在查询输入框中键入SQL语句
2. **执行查询**: 按Enter执行（仅当查询输入框有焦点时）
3. **切换回数据库选择**: 按Tab键切换焦点

**查询结果**
- 查询结果自动显示在界面下方
- 可以继续输入新的查询
- 可以切换到其他数据库重新查询

### 5. 示例查询
```sql
SHOW TABLES;
SELECT * FROM users LIMIT 10;
SELECT COUNT(*) FROM orders WHERE status = 'completed';
```

## 技术亮点

### 依赖库
- **github.com/charmbracelet/bubbletea**: 现代TUI框架
- **github.com/charmbracelet/bubbles**: 预制TUI组件
- **github.com/charmbracelet/lipgloss**: 样式和布局库

### 架构设计
- **Model-View-Update**: 使用 Elm 架构模式
- **事件驱动**: 响应式用户交互处理
- **异步查询**: 非阻塞数据库操作
- **状态管理**: 清晰的视图状态转换

### 代码特点
- 类型安全的消息传递
- 组合式UI组件
- 响应式布局调整
- 错误处理和状态反馈

## 对比旧实现

| 特性 | 旧版本 (tview) | 新版本 (Bubbles) |
|------|----------------|------------------|
| 界面美观度 | 基础 | 现代化设计 |
| 交互体验 | 传统 | 流畅自然 |
| 代码架构 | 命令式 | 函数式/声明式 |
| 维护性 | 中等 | 高 |
| 扩展性 | 有限 | 优秀 |

## 未来扩展

1. **语法高亮**: SQL语句语法着色
2. **自动补全**: 表名和字段自动补全
3. **查询历史**: 历史查询记录
4. **导出功能**: 结果导出为CSV/JSON
5. **多标签页**: 支持多个查询会话
