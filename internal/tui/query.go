package tui

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"dbtool/internal/config"
	"dbtool/internal/database"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// QueryTUI TUI查询界面
type QueryTUI struct {
	app            *tview.Application
	config         *config.Config
	layout         *tview.Flex
	dbList         *tview.List     // 左侧数据库列表
	tableList      *tview.List     // 左侧表列表
	sqlInput       *tview.TextArea // 上方SQL输入框
	resultView     *tview.Table    // 下方查询结果
	statusBar      *tview.TextView // 状态栏
	commandLine    *tview.TextView // Vim风格命令行
	commandMode    bool            // 命令模式标志
	commandBuffer  string          // 命令缓冲区
	commandHints   []string        // 命令提示列表
	commandHistory []string        // 命令历史
	historyIndex   int             // 历史索引
	currentDB      string          // 当前选中的数据库
	currentConn    *sql.DB         // 当前数据库连接
}

// NewQueryTUI 创建新的查询TUI
func NewQueryTUI(cfg *config.Config) *QueryTUI {
	tui := &QueryTUI{
		app:            tview.NewApplication(),
		config:         cfg,
		commandHints:   []string{"query", "quit", "db", "table", "commit"},
		commandHistory: []string{},
		historyIndex:   -1,
	}

	tui.setupUI()
	tui.setupKeybindings()

	return tui
}

// setupUI 设置界面布局
func (q *QueryTUI) setupUI() {
	// 数据库列表
	q.dbList = tview.NewList()
	q.dbList.SetBorder(true).SetTitle(" 数据库 ")
	q.dbList.ShowSecondaryText(false)

	// 添加数据库选项
	for name := range q.config.Databases {
		q.dbList.AddItem(name, "", 0, nil)
	}

	// 设置数据库选择回调
	q.dbList.SetSelectedFunc(q.selectDatabase)

	// 表列表
	q.tableList = tview.NewList()
	q.tableList.SetBorder(true).SetTitle(" 表 ")
	q.tableList.ShowSecondaryText(false)

	// SQL输入框
	q.sqlInput = tview.NewTextArea()
	q.sqlInput.SetPlaceholder("输入SQL查询语句... (按 :commit 执行)")
	q.sqlInput.SetBorder(true).SetTitle(" SQL查询 ")
	q.sqlInput.SetWrap(true)

	// 结果表格
	q.resultView = tview.NewTable()
	q.resultView.SetBorder(true).SetTitle(" 查询结果 ")
	q.resultView.SetSelectable(true, false)

	// 状态栏
	q.statusBar = tview.NewTextView()
	q.statusBar.SetDynamicColors(true)
	q.statusBar.SetText("[white]快捷键: [yellow]:query[white]聚焦查询 [yellow]:db[white]聚焦数据库 [yellow]:table[white]聚焦表 [yellow]:commit[white]执行查询 [yellow]:quit[white]退出")

	// Vim风格命令行
	q.commandLine = tview.NewTextView()
	q.commandLine.SetDynamicColors(true)
	q.commandLine.SetBorder(false)
	q.commandLine.SetText("")

	// 左侧面板 (数据库和表)
	leftPanel := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(q.dbList, 0, 1, true).
		AddItem(q.tableList, 0, 1, false)

	// 右侧面板 (SQL输入和结果)
	rightPanel := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(q.sqlInput, 8, 0, false).
		AddItem(q.resultView, 0, 1, false)

	// 主布局
	q.layout = tview.NewFlex().
		AddItem(leftPanel, 30, 0, true). // 左侧固定宽度30
		AddItem(rightPanel, 0, 1, false) // 右侧占剩余空间

	// 最终布局 - 添加Vim风格命令行
	mainLayout := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(q.layout, 0, 1, true).
		AddItem(q.commandLine, 1, 0, false).
		AddItem(q.statusBar, 1, 0, false)

	q.app.SetRoot(mainLayout, true)
	q.app.SetFocus(q.dbList)
}

// setupKeybindings 设置快捷键
func (q *QueryTUI) setupKeybindings() {
	q.app.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		// 处理命令模式
		if event.Rune() == ':' && !q.commandMode {
			q.commandMode = true
			q.commandBuffer = ""
			q.updateCommandLine()
			return nil
		}

		// 在命令模式下处理命令
		if q.commandMode {
			return q.handleCommand(event)
		}

		// 普通按键处理
		switch event.Key() {
		case tcell.KeyCtrlC:
			q.app.Stop()
			return nil
		case tcell.KeyEscape:
			q.commandMode = false
			q.commandBuffer = ""
			q.clearCommandLine()
			return nil
		}

		return event
	})
}

// handleCommand 处理命令模式输入
func (q *QueryTUI) handleCommand(event *tcell.EventKey) *tcell.EventKey {
	switch event.Key() {
	case tcell.KeyEnter:
		// 执行完整命令
		q.processCommand()
		q.commandMode = false
		q.commandBuffer = ""
		q.clearCommandLine()
		return nil

	case tcell.KeyEscape:
		q.commandMode = false
		q.commandBuffer = ""
		q.clearCommandLine()
		return nil

	case tcell.KeyBackspace, tcell.KeyBackspace2:
		if len(q.commandBuffer) > 0 {
			q.commandBuffer = q.commandBuffer[:len(q.commandBuffer)-1]
			q.updateCommandLine()
		}
		return nil

	case tcell.KeyTab:
		// Tab补全
		q.autoCompleteCommand()
		return nil

	case tcell.KeyUp:
		// 向上浏览历史
		q.navigateHistory(-1)
		return nil

	case tcell.KeyDown:
		// 向下浏览历史
		q.navigateHistory(1)
		return nil
	}

	// 添加字符到命令缓冲区
	if event.Rune() != 0 && event.Rune() >= 32 {
		q.commandBuffer += string(event.Rune())
		q.updateCommandLine()
	}

	return nil
}

// updateCommandLine 更新Vim风格命令行显示
func (q *QueryTUI) updateCommandLine() {
	if !q.commandMode {
		return
	}

	// 获取匹配的命令提示
	matches := q.getMatchingCommands()

	// 构建显示文本
	var displayText string
	if len(matches) > 0 {
		// 显示当前输入和匹配提示
		if len(q.commandBuffer) == 0 {
			displayText = fmt.Sprintf("[white]:[yellow]%s[white] [gray](可用: %s) [blue](↑/↓历史 Tab补全)[white]",
				q.commandBuffer, strings.Join(q.commandHints, " "))
		} else {
			displayText = fmt.Sprintf("[white]:[yellow]%s[white]", q.commandBuffer)

			if len(matches) == 1 && matches[0] != q.commandBuffer {
				// 显示唯一匹配的补全提示
				remaining := strings.TrimPrefix(matches[0], q.commandBuffer)
				displayText += fmt.Sprintf("[gray]%s[white] [blue](Tab补全)[white]", remaining)
			} else if len(matches) > 1 {
				// 显示多个匹配
				displayText += fmt.Sprintf(" [gray](匹配: %s)[white] [blue](Tab补全)[white]", strings.Join(matches, " "))
			}

			// 如果有历史，显示历史提示
			if len(q.commandHistory) > 0 {
				displayText += " [blue](↑/↓历史)[white]"
			}
		}
	} else {
		displayText = fmt.Sprintf("[white]:[red]%s[white] [gray](未知命令)[white]", q.commandBuffer)
	}

	// 更新命令行显示
	q.app.QueueUpdateDraw(func() {
		q.commandLine.SetText(displayText)
	})
}

// clearCommandLine 清空命令行
func (q *QueryTUI) clearCommandLine() {
	q.app.QueueUpdateDraw(func() {
		q.commandLine.SetText("")
	})
}

// getMatchingCommands 获取匹配的命令
func (q *QueryTUI) getMatchingCommands() []string {
	if q.commandBuffer == "" {
		return q.commandHints
	}

	var matches []string
	for _, cmd := range q.commandHints {
		if strings.HasPrefix(cmd, q.commandBuffer) {
			matches = append(matches, cmd)
		}
	}
	return matches
}

// autoCompleteCommand Tab自动补全命令
func (q *QueryTUI) autoCompleteCommand() {
	matches := q.getMatchingCommands()

	if len(matches) == 1 {
		// 只有一个匹配，直接补全
		q.commandBuffer = matches[0]
		q.updateCommandLine()
	} else if len(matches) > 1 {
		// 多个匹配，找到最长公共前缀
		commonPrefix := q.findCommonPrefix(matches)
		if len(commonPrefix) > len(q.commandBuffer) {
			q.commandBuffer = commonPrefix
			q.updateCommandLine()
		}
	}
}

// findCommonPrefix 找到字符串数组的最长公共前缀
func (q *QueryTUI) findCommonPrefix(strs []string) string {
	if len(strs) == 0 {
		return ""
	}

	prefix := strs[0]
	for i := 1; i < len(strs); i++ {
		for j := 0; j < len(prefix) && j < len(strs[i]); j++ {
			if prefix[j] != strs[i][j] {
				prefix = prefix[:j]
				break
			}
		}
		if len(strs[i]) < len(prefix) {
			prefix = prefix[:len(strs[i])]
		}
	}
	return prefix
}

// navigateHistory 导航命令历史
func (q *QueryTUI) navigateHistory(direction int) {
	if len(q.commandHistory) == 0 {
		return
	}

	// 计算新的历史索引
	newIndex := q.historyIndex + direction

	if direction < 0 { // 向上（更旧的命令）
		if q.historyIndex == -1 {
			// 从当前命令开始向上
			q.historyIndex = len(q.commandHistory) - 1
		} else if newIndex >= 0 {
			q.historyIndex = newIndex
		}
	} else { // 向下（更新的命令）
		if newIndex < len(q.commandHistory) {
			q.historyIndex = newIndex
		} else {
			// 回到当前输入
			q.historyIndex = -1
			q.commandBuffer = ""
			q.updateCommandLine()
			return
		}
	}

	// 设置历史命令
	if q.historyIndex >= 0 && q.historyIndex < len(q.commandHistory) {
		q.commandBuffer = q.commandHistory[q.historyIndex]
		q.updateCommandLine()
	}
}

// addToHistory 添加命令到历史
func (q *QueryTUI) addToHistory(command string) {
	if command == "" {
		return
	}

	// 避免重复添加相同的命令
	if len(q.commandHistory) > 0 && q.commandHistory[len(q.commandHistory)-1] == command {
		return
	}

	q.commandHistory = append(q.commandHistory, command)

	// 限制历史长度
	if len(q.commandHistory) > 50 {
		q.commandHistory = q.commandHistory[1:]
	}

	q.historyIndex = -1 // 重置历史索引
}

// processCommand 处理完整命令
func (q *QueryTUI) processCommand() {
	if q.commandBuffer != "" {
		// 保存到历史
		q.addToHistory(q.commandBuffer)
	}

	switch q.commandBuffer {
	case "query":
		q.executeCommand("query")
	case "quit":
		q.executeCommand("quit")
	case "db":
		q.executeCommand("db")
	case "table":
		q.executeCommand("table")
	case "commit":
		q.executeCommand("commit")
	default:
		q.setStatus(fmt.Sprintf("[red]未知命令: %s[white]", q.commandBuffer))
	}
}

// executeCommand 执行命令
func (q *QueryTUI) executeCommand(cmd string) {
	switch cmd {
	case "query":
		q.app.SetFocus(q.sqlInput)
		q.setStatus("[green]已聚焦到SQL查询框[white]")
	case "db":
		q.app.SetFocus(q.dbList)
		q.setStatus("[green]已聚焦到数据库列表[white]")
	case "table":
		q.app.SetFocus(q.tableList)
		q.setStatus("[green]已聚焦到表列表[white]")
	case "commit":
		q.executeQuery()
	case "quit":
		q.app.Stop()
	}
}

// selectDatabase 选择数据库
func (q *QueryTUI) selectDatabase(index int, mainText, secondaryText string, shortcut rune) {
	q.currentDB = mainText
	q.setStatus(fmt.Sprintf("[green]已选择数据库: %s[white]", mainText))

	// 连接数据库并获取表列表
	go q.loadTables(mainText)
}

// loadTables 加载数据库表列表
func (q *QueryTUI) loadTables(dbName string) {
	dbConfig, exists := q.config.GetDatabase(dbName)
	if !exists {
		q.setStatus(fmt.Sprintf("[red]数据库配置不存在: %s[white]", dbName))
		return
	}

	driver, err := database.GetDriver(dbConfig.Driver)
	if err != nil {
		q.setStatus(fmt.Sprintf("[red]获取数据库驱动失败: %v[white]", err))
		return
	}

	// 连接数据库
	db, err := driver.Connect(dbConfig)
	if err != nil {
		q.setStatus(fmt.Sprintf("[red]连接数据库失败: %v[white]", err))
		return
	}

	// 保存连接以供后续使用
	if q.currentConn != nil {
		q.currentConn.Close()
	}
	q.currentConn = db

	// 查询表列表
	var query string
	switch dbConfig.Driver {
	case "mysql":
		query = "SHOW TABLES"
	default:
		q.setStatus(fmt.Sprintf("[red]不支持的数据库类型: %s[white]", dbConfig.Driver))
		return
	}

	rows, err := db.Query(query)
	if err != nil {
		q.setStatus(fmt.Sprintf("[red]查询表列表失败: %v[white]", err))
		return
	}
	defer rows.Close()

	// 更新表列表
	q.app.QueueUpdateDraw(func() {
		q.tableList.Clear()
		for rows.Next() {
			var tableName string
			if err := rows.Scan(&tableName); err == nil {
				q.tableList.AddItem(tableName, "", 0, nil)
			}
		}
		q.tableList.SetTitle(fmt.Sprintf(" 表 (%s) ", dbName))
	})

	q.setStatus(fmt.Sprintf("[green]已加载数据库 %s 的表列表[white]", dbName))
}

// executeQuery 执行查询
func (q *QueryTUI) executeQuery() {
	// 检查是否选择了数据库
	if q.currentDB == "" {
		q.setStatus("[red]请先选择数据库[white]")
		return
	}

	// 获取SQL语句
	sqlText := strings.TrimSpace(q.sqlInput.GetText())
	if sqlText == "" {
		q.setStatus("[red]请输入SQL查询语句[white]")
		return
	}

	q.setStatus("[yellow]正在执行查询...[white]")

	// 获取数据库配置
	dbConfig, exists := q.config.GetDatabase(q.currentDB)
	if !exists {
		q.setStatus("[red]数据库配置不存在[white]")
		return
	}

	// 执行查询
	go q.performQuery(dbConfig, sqlText)
}

// performQuery 执行查询（异步）
func (q *QueryTUI) performQuery(dbConfig config.DatabaseConfig, sqlText string) {
	// 使用现有连接或创建新连接
	var db *sql.DB
	var err error

	if q.currentConn != nil {
		db = q.currentConn
	} else {
		driver, err := database.GetDriver(dbConfig.Driver)
		if err != nil {
			q.setStatus(fmt.Sprintf("[red]获取数据库驱动失败: %v[white]", err))
			return
		}

		db, err = driver.Connect(dbConfig)
		if err != nil {
			q.setStatus(fmt.Sprintf("[red]连接数据库失败: %v[white]", err))
			return
		}
		defer db.Close()
	}

	// 设置查询超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	rows, err := db.QueryContext(ctx, sqlText)
	if err != nil {
		q.setStatus(fmt.Sprintf("[red]查询执行失败: %v[white]", err))
		return
	}
	defer rows.Close()

	// 显示结果
	if err := q.displayResults(rows); err != nil {
		q.setStatus(fmt.Sprintf("[red]显示结果失败: %v[white]", err))
		return
	}

	q.setStatus("[green]查询执行成功[white]")
}

// displayResults 显示查询结果
func (q *QueryTUI) displayResults(rows *sql.Rows) error {
	q.app.QueueUpdateDraw(func() {
		// 清空之前的结果
		q.resultView.Clear()

		// 获取列名
		columns, err := rows.Columns()
		if err != nil {
			q.setStatus(fmt.Sprintf("[red]获取列信息失败: %v[white]", err))
			return
		}

		// 设置表头
		for i, col := range columns {
			q.resultView.SetCell(0, i, tview.NewTableCell(col).
				SetTextColor(tcell.ColorYellow).
				SetAlign(tview.AlignCenter).
				SetSelectable(false).
				SetAttributes(tcell.AttrBold))
		}

		// 读取数据
		rowIndex := 1
		for rows.Next() {
			// 创建接收数据的slice
			values := make([]interface{}, len(columns))
			valuePtrs := make([]interface{}, len(columns))
			for i := range values {
				valuePtrs[i] = &values[i]
			}

			// 扫描数据
			if err := rows.Scan(valuePtrs...); err != nil {
				q.setStatus(fmt.Sprintf("[red]扫描数据失败: %v[white]", err))
				return
			}

			// 添加到表格
			for i, val := range values {
				var cellText string
				if val == nil {
					cellText = "NULL"
				} else {
					cellText = fmt.Sprintf("%v", val)
				}

				color := tcell.ColorWhite
				if val == nil {
					color = tcell.ColorGray
				}

				q.resultView.SetCell(rowIndex, i, tview.NewTableCell(cellText).
					SetTextColor(color).
					SetAlign(tview.AlignLeft))
			}
			rowIndex++
		}

		// 更新标题显示记录数
		q.resultView.SetTitle(fmt.Sprintf(" 查询结果 (%d行) ", rowIndex-1))
	})

	return nil
}

// setStatus 设置状态栏信息
func (q *QueryTUI) setStatus(text string) {
	q.app.QueueUpdateDraw(func() {
		q.statusBar.SetText(text)
	})
}

// Run 运行TUI
func (q *QueryTUI) Run() error {
	defer func() {
		if q.currentConn != nil {
			q.currentConn.Close()
		}
	}()

	return q.app.Run()
}
