package tui

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"dbtool/internal/config"
	"dbtool/internal/database"

	"github.com/charmbracelet/bubbles/table"
	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// 视图状态
type viewState int

const (
	viewDBSelector   viewState = iota // 数据库选择界面
	viewQueryInput                    // 查询输入界面
	viewResult                        // 结果显示界面
	viewColumnDetail                  // 列详情查看界面
)

// 焦点状态
type focusState int

const (
	focusDBSelector  focusState = iota // 焦点在数据库选择
	focusQueryInput                    // 焦点在查询输入
	focusResultTable                   // 焦点在结果表格
)

// 查询模型
type QueryModel struct {
	config      *config.Config
	textInput   textinput.Model
	table       table.Model
	state       viewState
	focus       focusState
	currentDB   string
	currentConn *sql.DB
	width       int
	height      int
	err         error
	statusMsg   string
	executing   bool
	dbNames     []string
	selectedDB  int
	dbSelected  bool // 是否已选择数据库
	showResults bool // 是否显示查询结果
	// 新增：列导航支持
	selectedColumn int            // 当前选中的列索引
	originalCols   []table.Column // 保存原始列信息
	originalRows   []table.Row    // 保存原始行数据
	// 列详情查看
	columnDetailValue string // 当前查看的列值
	columnDetailName  string // 当前查看的列名
	offset            int    // 偏移量
}

// 消息类型
type queryResultMsg struct {
	columns []string
	rows    [][]string
	err     error
}

type queryExecuteMsg struct{}

// 样式定义 - 经典黑白灰配色
var (
	// 黑白灰配色方案
	colorPrimary   = lipgloss.Color("#FFFFFF") // 白色 - 主要高亮
	colorSecondary = lipgloss.Color("#D0D0D0") // 浅灰 - 次要高亮
	colorAccent    = lipgloss.Color("#A0A0A0") // 中灰 - 强调色
	colorWarning   = lipgloss.Color("#808080") // 深灰 - 警告
	colorError     = lipgloss.Color("#606060") // 更深灰 - 错误
	colorBg        = lipgloss.Color("#000000") // 纯黑 - 背景
	colorSurface   = lipgloss.Color("#1A1A1A") // 深灰 - 表面
	colorBorder    = lipgloss.Color("#404040") // 中灰 - 边框
	colorText      = lipgloss.Color("#E0E0E0") // 浅灰 - 文本
	colorTextDim   = lipgloss.Color("#808080") // 暗灰 - 次要文本
	colorFocus     = lipgloss.Color("#87CEEB") // 浅蓝色 - 聚焦效果

	// 主容器样式 - 黑白灰风格
	containerStyle = lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(colorBorder).
			Padding(1, 2)

	// 单行文本框样式 - 无边框简洁设计
	textInputStyle = lipgloss.NewStyle().
			Foreground(colorText).
			Background(colorSurface).
			Padding(0, 1).
			Margin(0, 0)

	textInputFocusedStyle = lipgloss.NewStyle().
				Foreground(colorPrimary).
				Background(colorSurface).
				Padding(0, 1).
				Margin(0, 0)

	// 表格样式 - 黑白灰风格
	tableStyle = lipgloss.NewStyle().
			BorderStyle(lipgloss.NormalBorder()).
			BorderForeground(colorBorder)

	statusStyle = lipgloss.NewStyle().
			Foreground(colorTextDim).
			Margin(1, 0)

	errorStyle = lipgloss.NewStyle().
			Foreground(colorError).
			Background(colorSurface).
			Padding(0, 2).
			Margin(1, 0).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(colorError)

	successStyle = lipgloss.NewStyle().
			Foreground(colorAccent).
			Background(colorSurface).
			Padding(0, 2).
			Margin(1, 0).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(colorAccent)

	titleStyle = lipgloss.NewStyle().
			Foreground(colorPrimary).
			Bold(true).
			Align(lipgloss.Center).
			Margin(0, 0, 1, 0)

	helpStyle = lipgloss.NewStyle().
			Foreground(colorTextDim).
			Italic(true).
			Margin(1, 0)

	// 数据库选择相关样式 - Simple List风格
	dbSelectorStyle = lipgloss.NewStyle().
			Padding(0, 1).
			Margin(0, 0)

	dbItemStyle = lipgloss.NewStyle().
			Foreground(colorTextDim).
			Padding(0, 1).
			Margin(0, 0)

	dbItemSelectedStyle = lipgloss.NewStyle().
				Foreground(colorText).
				Padding(0, 1).
				Margin(0, 0)

	dbItemFocusedStyle = lipgloss.NewStyle().
				Foreground(colorPrimary).
				Background(colorSurface).
				Padding(0, 1).
				Margin(0, 0).
				Bold(true)

	promptStyle = lipgloss.NewStyle().
			Foreground(colorText).
			Bold(true).
			Margin(0, 0, 1, 0)

	// 查询提示样式 - 浅灰高亮
	queryPromptStyle = lipgloss.NewStyle().
				Foreground(colorBg).
				Background(colorSecondary).
				Padding(0, 1).
				Bold(true)
)

// NewQueryModel 创建新的查询模型
func NewQueryModel(cfg *config.Config) QueryModel {
	// 初始化单行文本输入框
	ti := textinput.New()
	ti.Placeholder = "输入SQL查询语句..."
	ti.Focus()
	ti.CharLimit = 1000
	ti.Width = 60 // 初始宽度，会动态调整

	// 初始化表格 - 参考官方示例的最佳实践
	columns := []table.Column{}

	// 创建表格样式 - 黑白灰风格
	s := table.DefaultStyles()
	s.Header = s.Header.
		BorderStyle(lipgloss.NormalBorder()).
		BorderForeground(colorBorder).
		Foreground(colorPrimary).
		Background(colorSurface).
		BorderBottom(true).
		Bold(true)
	s.Selected = s.Selected.
		Foreground(colorFocus).
		Background(colorBg).
		Bold(true)
	s.Cell = s.Cell.
		Foreground(colorText).
		Background(colorBg)

	tb := table.New(
		table.WithColumns(columns),
		table.WithRows([]table.Row{}),
		table.WithFocused(false), // 初始化时不设置焦点
		table.WithHeight(10),
		table.WithStyles(s),
	)

	// 获取数据库列表
	dbNames := make([]string, 0, len(cfg.Databases))
	for name := range cfg.Databases {
		dbNames = append(dbNames, name)
	}

	// 默认自动选择第一个数据库并显示查询输入框
	var initialState viewState = viewQueryInput
	var initialFocus focusState = focusQueryInput
	var dbSelected bool = true
	var currentDB string = ""

	if len(dbNames) > 0 {
		// 自动选择第一个数据库并切换到查询输入
		currentDB = dbNames[0]
		ti.Focus() // 确保文本输入框获得焦点
	} else {
		// 没有数据库配置时的异常情况
		initialState = viewDBSelector
		initialFocus = focusDBSelector
		dbSelected = false
	}

	m := QueryModel{
		config:      cfg,
		textInput:   ti,
		table:       tb,
		state:       initialState,
		focus:       initialFocus,
		currentDB:   currentDB,
		dbNames:     dbNames,
		selectedDB:  0,
		dbSelected:  dbSelected,
		showResults: false,
	}

	return m
}

// Init 初始化模型
func (m QueryModel) Init() tea.Cmd {
	return textinput.Blink
}

// Update 更新模型
func (m QueryModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		// 优先处理全局快捷键（Tab、Ctrl+C），避免被textInput拦截
		switch msg.String() {
		case "ctrl+c":
			// 关闭数据库连接
			if m.currentConn != nil {
				m.currentConn.Close()
			}
			return m, tea.Quit

		case "esc":
			if m.state == viewColumnDetail {
				// 从列详情视图返回结果视图
				m.state = viewResult
			}

		case "tab":
			// Tab键循环切换焦点：数据库选择 -> 查询输入 -> 结果表格 -> 数据库选择
			switch m.focus {
			case focusDBSelector:
				m.focus = focusQueryInput
				m.textInput.Focus()
				m.table.Blur() // 确保表格失去焦点
				cmds = append(cmds, textinput.Blink)
				if m.dbSelected {
					m.state = viewQueryInput
				}
			case focusQueryInput:
				if m.showResults {
					// 如果有查询结果，切换到表格焦点
					m.focus = focusResultTable
					m.textInput.Blur()
					// 重新创建表格以确保焦点状态正确
					m.recreateTableWithFocus(true)
				} else {
					// 没有查询结果，切回数据库选择
					m.focus = focusDBSelector
					m.textInput.Blur()
					m.state = viewDBSelector
				}
			case focusResultTable:
				// 从表格焦点切回数据库选择
				m.focus = focusDBSelector
				m.state = viewDBSelector
				// 重新创建表格以确保失去焦点状态正确
				m.recreateTableWithFocus(false)
			}
			// Tab键处理完毕，不再向下传递给textInput
			return m, tea.Batch(cmds...)

		case "enter":
			if m.focus == focusDBSelector {
				// 选择数据库
				if len(m.dbNames) > 0 {
					m.currentDB = m.dbNames[m.selectedDB]
					m.dbSelected = true
					// 选择数据库后自动切换到查询输入框
					m.focus = focusQueryInput
					m.state = viewQueryInput
					m.textInput.Focus()
					cmds = append(cmds, textinput.Blink)
				}
			} else if m.focus == focusQueryInput && m.state == viewQueryInput {
				// 执行查询（只有查询输入框有焦点时才执行）
				return m, m.executeQuery()
			} else if m.focus == focusResultTable && m.showResults && len(m.originalCols) > 0 && len(m.originalRows) > 0 {
				// 定位到表格中选中的列位置，而不是进入列详情查看模式
				// 这里可以添加高亮显示选中列的逻辑，或者直接保持当前状态
				// 如果需要查看列详情，可以使用其他快捷键，比如 'v' 键
				m.offset = m.selectedColumn
				m.updateTableDisplay()
			}

		case "up", "down", "j", "k", "page_up", "page_down", "home", "end":
			if m.focus == focusDBSelector {
				// 数据库选择导航（只响应上下键）
				if msg.String() == "up" || msg.String() == "k" {
					m.selectedDB = (m.selectedDB - 1 + len(m.dbNames)) % len(m.dbNames)
				} else if msg.String() == "down" || msg.String() == "j" {
					m.selectedDB = (m.selectedDB + 1) % len(m.dbNames)
				}
			} else if m.focus == focusResultTable && m.showResults {
				// 结果表格导航 - 只支持上下行导航（table组件的原生功能）
				m.table, cmd = m.table.Update(msg)
				cmds = append(cmds, cmd)
				// 表格导航处理完毕，直接返回，避免重复更新
				return m, tea.Batch(cmds...)
			}

		// 列导航功能 - 使用左右箭头键和vim风格键
		case "left", "h":
			if m.focus == focusResultTable && m.showResults && len(m.originalCols) > 0 {
				// 向左移动列
				m.selectedColumn--
				if m.selectedColumn < 0 {
					m.selectedColumn = len(m.originalCols) - 1 // 循环到最后一列
				}
			}
		case "right", "l":
			if m.focus == focusResultTable && m.showResults && len(m.originalCols) > 0 {
				// 向右移动列
				m.selectedColumn++
				if m.selectedColumn >= len(m.originalCols) {
					m.selectedColumn = 0 // 循环到第一列
				}
			}
		case "v":
			if m.focus == focusResultTable && m.showResults && len(m.originalCols) > 0 && len(m.originalRows) > 0 {
				// 使用 'v' 键进入列详情查看模式
				currentRow := m.table.Cursor()
				if currentRow < len(m.originalRows) && m.selectedColumn < len(m.originalRows[currentRow]) {
					m.columnDetailValue = m.originalRows[currentRow][m.selectedColumn]
					m.columnDetailName = m.originalCols[m.selectedColumn].Title
					m.state = viewColumnDetail
				}
			}

		}

	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height

		// 响应式调整组件大小
		textInputWidth := max(m.width-10, 40) // 最小40字符，否则使用100%宽度减去边距
		m.textInput.Width = textInputWidth

		// 更新表格大小 - 响应式宽度，黑白灰风格
		if len(m.table.Columns()) > 0 {
			s := table.DefaultStyles()
			s.Header = s.Header.
				BorderStyle(lipgloss.NormalBorder()).
				BorderForeground(colorBorder).
				Foreground(colorPrimary).
				Background(colorSurface).
				BorderBottom(true).
				Bold(true)

			// 关键：根据当前焦点状态设置选中样式
			if m.focus == focusResultTable {
				s.Selected = s.Selected.
					Foreground(lipgloss.Color("#87CEEB")). // 浅蓝色前景
					Background(lipgloss.Color("#000000")). // 黑色背景
					Bold(true).
					Reverse(true) // 尝试使用反转效果
			} else {
				// 没有焦点时使用默认样式
				s.Selected = s.Selected.
					Foreground(colorText). // 普通文本颜色
					Background(colorBg).   // 黑色背景
					Bold(false)
			}

			s.Cell = s.Cell.
				Foreground(colorText).
				Background(colorBg)
			m.table.SetStyles(s)

			// 根据当前焦点状态设置表格焦点
			if m.focus == focusResultTable {
				m.table.Focus()
			} else {
				m.table.Blur()
			}
		}

	case queryResultMsg:
		// 查询结果返回
		m.executing = false
		if msg.err != nil {
			m.err = msg.err
			m.statusMsg = fmt.Sprintf("查询失败: %v", msg.err)
		} else {
			// 更新表格 - 支持水平滚动的智能列宽计算
			columns := make([]table.Column, len(msg.columns))

			// 固定列宽为20
			colWidths := make([]int, len(msg.columns))
			for i := range msg.columns {
				colWidths[i] = 20 // 固定列宽为20字符
			}

			// 初始化列导航
			m.selectedColumn = 0

			// 确保列数组索引安全
			if len(msg.columns) != len(colWidths) {
				m.err = fmt.Errorf("列数不匹配: %d vs %d", len(msg.columns), len(colWidths))
				return m, nil
			}

			for i, col := range msg.columns {
				if i >= len(colWidths) {
					continue // 跳过超出范围的列
				}
				columns[i] = table.Column{
					Title: col,
					Width: colWidths[i],
				}
			}

			// 保存原始列信息用于滚动
			m.originalCols = make([]table.Column, len(columns))
			copy(m.originalCols, columns)

			rows := make([]table.Row, len(msg.rows))
			for i, row := range msg.rows {
				rows[i] = table.Row(row)
			}

			// 保存原始行数据用于滚动
			m.originalRows = make([]table.Row, len(rows))
			copy(m.originalRows, rows)

			// 智能调整表格高度 - 支持滚动浏览大量数据
			maxDisplayRows := max(10, m.height-15)              // 为其他UI元素预留空间
			tableHeight := min(len(msg.rows)+3, maxDisplayRows) // 包含header行

			// 如果数据行数多于显示行数，启用滚动功能
			if len(msg.rows) > maxDisplayRows-3 {
				tableHeight = maxDisplayRows
			}

			// 应用黑白灰风格的表格样式，优化导航体验
			s := table.DefaultStyles()
			s.Header = s.Header.
				BorderStyle(lipgloss.NormalBorder()).
				BorderForeground(colorBorder).
				Foreground(colorPrimary).
				Background(colorSurface).
				BorderBottom(true).
				Bold(true)

			// 关键：根据当前焦点状态设置选中样式
			if m.focus == focusResultTable {
				s.Selected = s.Selected.
					Foreground(lipgloss.Color("#87CEEB")). // 浅蓝色前景
					Background(lipgloss.Color("#000000")). // 黑色背景
					Bold(true).
					Reverse(true) // 尝试使用反转效果
			} else {
				// 没有焦点时使用默认样式
				s.Selected = s.Selected.
					Foreground(colorText). // 普通文本颜色
					Background(colorBg).   // 黑色背景
					Bold(false)
			}

			s.Cell = s.Cell.
				Foreground(colorText).
				Background(colorBg)

			m.table = table.New(
				table.WithColumns(columns),
				table.WithRows(rows),
				table.WithFocused(m.focus == focusResultTable), // 根据当前焦点状态设置
				table.WithHeight(tableHeight),
				table.WithStyles(s),
			)
			m.showResults = true // 显示结果在上方
			m.err = nil

			// 如果当前焦点在表格，则设置表格焦点并确保选中第一行
			if m.focus == focusResultTable {
				m.table.Focus()
				// 确保有行被选中，强制移动到第一行
				if len(rows) > 0 {
					// 通过发送键盘事件来确保选中状态被激活
					m.table.MoveUp(1)   // 先向上
					m.table.MoveDown(1) // 再向下，这样确保选中状态被激活
				}
			}
		}

	case queryExecuteMsg:
		m.executing = true
		m.statusMsg = "正在执行查询..."
		m.err = nil
	}

	// 更新文本输入（只有在查询输入焦点时才更新）
	if m.focus == focusQueryInput {
		m.textInput, cmd = m.textInput.Update(msg)
		cmds = append(cmds, cmd)
	}

	// 表格更新已在上面的键盘事件中处理，这里不再重复更新

	return m, tea.Batch(cmds...)
}

// View 渲染视图
func (m QueryModel) View() string {
	var sections []string

	// 状态消息 - 只显示错误或重要信息
	if m.err != nil {
		sections = append(sections, errorStyle.Width(m.width-6).Render(m.statusMsg))
	} else if m.executing {
		sections = append(sections, statusStyle.Width(m.width-6).Render(m.statusMsg))
	}

	// 首先显示输入界面 - 根据状态显示不同的界面
	switch m.state {
	case viewDBSelector:
		// 数据库选择界面
		sections = append(sections, m.renderDBSelectorView())
	case viewQueryInput:
		// 查询输入界面
		sections = append(sections, m.renderQueryInputView())
	case viewColumnDetail:
		// 列详情查看界面
		sections = append(sections, m.renderColumnDetailView())
		return strings.Join(sections, "\n") // 列详情视图独占整个屏幕
	}

	// 查询结果（在输入框下方显示）
	if m.showResults {
		sections = append(sections, "")
		sections = append(sections, m.renderResultView())
	}

	return strings.Join(sections, "\n")
}

// renderDBSelectorView 渲染数据库选择视图 - Simple List风格
func (m QueryModel) renderDBSelectorView() string {
	var elements []string

	// Simple List风格的数据库选择
	if len(m.dbNames) > 0 {
		for i, dbName := range m.dbNames {
			var item string
			if i == m.selectedDB && m.focus == focusDBSelector {
				// 当前选中且有焦点 - 简洁高亮
				item = dbItemFocusedStyle.Render(fmt.Sprintf("▶ %s", dbName))
			} else if i == m.selectedDB {
				// 当前选中但无焦点
				item = dbItemSelectedStyle.Render(fmt.Sprintf("• %s", dbName))
			} else {
				// 未选中 - 简洁显示
				item = dbItemStyle.Render(fmt.Sprintf("  %s", dbName))
			}
			elements = append(elements, item)
		}
	}

	return strings.Join(elements, "\n")
}

// renderQueryInputView 渲染查询输入视图
func (m QueryModel) renderQueryInputView() string {
	var elements []string

	// 赛博朋克风格的数据库信息
	if m.currentDB != "" && len(m.dbNames) > 1 {
		dbInfo := fmt.Sprintf("◉ %s", m.currentDB) // 使用圆点符号
		elements = append(elements, queryPromptStyle.Render(dbInfo))
	}

	// 单行SQL输入框 - 无边框简洁设计
	if m.focus == focusQueryInput {
		// 有焦点时显示白色高亮
		prompt := "▶ "
		styledPrompt := lipgloss.NewStyle().
			Foreground(colorPrimary).
			Bold(true).
			Render(prompt)

		inputContent := m.textInput.View()
		if inputContent == "" {
			inputContent = lipgloss.NewStyle().
				Foreground(colorTextDim).
				Render("输入SQL查询...")
		}

		fullInput := styledPrompt + inputContent
		textInputContent := lipgloss.NewStyle().
			Foreground(colorPrimary).
			Background(colorSurface).
			Padding(0, 1).
			Render(fullInput)
		elements = append(elements, textInputContent)
	} else {
		// 无焦点时显示简化版本
		prompt := "▷ "
		inputValue := m.textInput.Value()
		if inputValue == "" {
			inputValue = "输入SQL查询..."
		}
		fullInput := prompt + inputValue
		textInputContent := lipgloss.NewStyle().
			Foreground(colorText).
			Background(colorSurface).
			Padding(0, 1).
			Render(fullInput)
		elements = append(elements, textInputContent)
	}

	return strings.Join(elements, "\n")
}

// renderResultView 渲染结果视图
func (m QueryModel) renderResultView() string {
	var elements []string

	// 根据焦点状态显示不同的标题
	if m.focus == focusResultTable {
		elements = append(elements, titleStyle.Render("查询结果 (focus)"))
	} else {
		elements = append(elements, titleStyle.Render("查询结果"))
	}

	// 使用原生表格渲染，确保显示稳定
	tableView := m.table.View()

	// 如果表格有很多行，添加滚动条指示器
	if len(m.table.Rows()) > m.table.Height()-1 && m.focus == focusResultTable {
		tableView = m.addScrollIndicator(tableView)
	}

	elements = append(elements, tableView)

	// 在有焦点时显示当前选中列的信息
	if m.focus == focusResultTable && len(m.originalCols) > 0 {
		currentColInfo := fmt.Sprintf("当前列: %s", m.originalCols[m.selectedColumn].Title)
		styledColInfo := lipgloss.NewStyle().
			Foreground(colorPrimary).
			Bold(true).
			Render(currentColInfo)
		elements = append(elements, styledColInfo)
	}

	// 在有焦点时显示导航提示
	if m.focus == focusResultTable {
		// 显示当前行数信息和导航提示
		rowInfo := fmt.Sprintf("第 %d 行，共 %d 行",
			m.table.Cursor()+1, len(m.table.Rows()))

		// 添加列导航信息
		colInfo := ""
		if len(m.originalCols) > 0 {
			colName := m.originalCols[m.selectedColumn].Title
			colInfo = fmt.Sprintf(" | 列: %s (%d/%d)", colName, m.selectedColumn+1, len(m.originalCols))
		}

		helpText := fmt.Sprintf("%s%s | ↑↓jk:行导航 ←→hl:列导航 Enter:定位到表格位置 v:查看列值 PgUp/PgDn:翻页 Home/End:首末 Tab:切换焦点",
			rowInfo, colInfo)

		styledHelp := lipgloss.NewStyle().
			Foreground(colorTextDim).
			Italic(true).
			Render(helpText)
		elements = append(elements, styledHelp)
	}

	return strings.Join(elements, "\n")
}

// addScrollIndicator 为表格添加滚动条指示器
func (m QueryModel) addScrollIndicator(tableView string) string {
	lines := strings.Split(tableView, "\n")
	if len(lines) == 0 {
		return tableView
	}

	totalRows := len(m.table.Rows())
	visibleRows := m.table.Height() - 1 // 减去header行
	currentPos := m.table.Cursor()

	// 计算滚动位置
	scrollBarHeight := len(lines) - 2 // 减去顶部和底部边框
	if scrollBarHeight <= 0 {
		return tableView
	}

	// 计算滚动条位置
	scrollPos := 0
	if totalRows > visibleRows {
		scrollPos = (currentPos * scrollBarHeight) / totalRows
		if scrollPos >= scrollBarHeight {
			scrollPos = scrollBarHeight - 1
		}
	}

	// 添加滚动条
	for i, line := range lines {
		if i == 0 || i == len(lines)-1 {
			// 顶部和底部边框不添加滚动条
			continue
		}

		lineIndex := i - 1 // 调整为滚动条索引
		if lineIndex == scrollPos && totalRows > visibleRows {
			// 当前滚动位置
			lines[i] = line + " ■"
		} else if lineIndex < scrollBarHeight && totalRows > visibleRows {
			// 滚动条轨道
			lines[i] = line + " │"
		}
	}

	return strings.Join(lines, "\n")
}

// renderHelp 渲染帮助信息（简化版，通常不显示）
func (m QueryModel) renderHelp() string {
	// 简化后基本不显示帮助信息，保持界面简洁
	return ""
}

// executeQuery 执行查询
func (m QueryModel) executeQuery() tea.Cmd {
	if m.currentDB == "" {
		return func() tea.Msg {
			return queryResultMsg{err: fmt.Errorf("请先选择数据库")}
		}
	}

	sqlText := strings.TrimSpace(m.textInput.Value())
	if sqlText == "" {
		return func() tea.Msg {
			return queryResultMsg{err: fmt.Errorf("请输入SQL查询语句")}
		}
	}

	return tea.Batch(
		func() tea.Msg { return queryExecuteMsg{} },
		func() tea.Msg {
			return m.performQuery(sqlText)
		},
	)
}

// performQuery 执行查询
func (m QueryModel) performQuery(sqlText string) queryResultMsg {
	// 获取数据库配置
	dbConfig, exists := m.config.GetDatabase(m.currentDB)
	if !exists {
		return queryResultMsg{err: fmt.Errorf("数据库配置不存在: %s", m.currentDB)}
	}

	// 连接数据库
	driver, err := database.GetDriver(dbConfig.Driver)
	if err != nil {
		return queryResultMsg{err: fmt.Errorf("获取数据库驱动失败: %v", err)}
	}

	db, err := driver.Connect(dbConfig)
	if err != nil {
		return queryResultMsg{err: fmt.Errorf("连接数据库失败: %v", err)}
	}
	defer db.Close()

	// 设置查询超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 执行查询
	rows, err := db.QueryContext(ctx, sqlText)
	if err != nil {
		return queryResultMsg{err: fmt.Errorf("查询执行失败: %v", err)}
	}
	defer rows.Close()

	// 获取列名
	columns, err := rows.Columns()
	if err != nil {
		return queryResultMsg{err: fmt.Errorf("获取列信息失败: %v", err)}
	}

	// 读取数据
	var resultRows [][]string
	for rows.Next() {
		// 创建接收数据的slice
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// 扫描数据
		if err := rows.Scan(valuePtrs...); err != nil {
			return queryResultMsg{err: fmt.Errorf("扫描数据失败: %v", err)}
		}

		// 转换为字符串
		row := make([]string, len(columns))
		for i, val := range values {
			if val == nil {
				row[i] = "NULL"
			} else {
				// 根据值的类型进行特殊处理
				switch v := val.(type) {
				case []byte:
					row[i] = string(v)
				case string:
					row[i] = v
				case int, int8, int16, int32, int64:
					row[i] = fmt.Sprintf("%d", v)
				case uint, uint8, uint16, uint32, uint64:
					row[i] = fmt.Sprintf("%d", v)
				case float32, float64:
					row[i] = fmt.Sprintf("%.2f", v)
				case bool:
					if v {
						row[i] = "true"
					} else {
						row[i] = "false"
					}
				case time.Time:
					row[i] = v.Format("2006-01-02 15:04:05")
				default:
					row[i] = fmt.Sprintf("%v", v)
				}
			}
		}
		resultRows = append(resultRows, row)
	}

	if err := rows.Err(); err != nil {
		return queryResultMsg{err: fmt.Errorf("遍历结果失败: %v", err)}
	}

	return queryResultMsg{columns: columns, rows: resultRows}
}

// renderColumnDetailView 渲染列详情查看视图
func (m QueryModel) renderColumnDetailView() string {
	var elements []string

	// 标题
	title := fmt.Sprintf("列详情: %s", m.columnDetailName)
	elements = append(elements, titleStyle.Render(title))
	elements = append(elements, "")

	// 列值内容框
	contentBox := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(colorBorder).
		Padding(1).
		Width(m.width - 4).
		Height(m.height - 8) // 为标题和帮助文本预留空间

	// 如果内容很长，支持换行显示
	wrappedContent := m.wrapText(m.columnDetailValue, m.width-8)
	elements = append(elements, contentBox.Render(wrappedContent))

	// 帮助信息
	helpText := "ESC: 返回表格视图 | Ctrl+C: 退出"
	styledHelp := lipgloss.NewStyle().
		Foreground(colorTextDim).
		Italic(true).
		Render(helpText)
	elements = append(elements, "")
	elements = append(elements, styledHelp)

	return strings.Join(elements, "\n")
}

// wrapText 文本换行辅助函数
func (m QueryModel) wrapText(text string, width int) string {
	if len(text) <= width {
		return text
	}

	var lines []string
	words := strings.Fields(text)
	currentLine := ""

	for _, word := range words {
		if len(currentLine)+len(word)+1 <= width {
			if currentLine == "" {
				currentLine = word
			} else {
				currentLine += " " + word
			}
		} else {
			if currentLine != "" {
				lines = append(lines, currentLine)
			}
			currentLine = word
		}
	}

	if currentLine != "" {
		lines = append(lines, currentLine)
	}

	return strings.Join(lines, "\n")
}

// renderTableWithColumnHighlight 已移除，使用原生table组件确保显示稳定

// recreateTableWithFocus 重新创建表格并设置正确的焦点状态
func (m *QueryModel) recreateTableWithFocus(focused bool) {
	if !m.showResults || len(m.originalCols) == 0 || len(m.originalRows) == 0 {
		return
	}

	// 智能调整表格高度 - 支持滚动浏览大量数据
	maxDisplayRows := max(10, m.height-15)                    // 为其他UI元素预留空间
	tableHeight := min(len(m.originalRows)+3, maxDisplayRows) // 包含header行

	// 如果数据行数多于显示行数，启用滚动功能
	if len(m.originalRows) > maxDisplayRows-3 {
		tableHeight = maxDisplayRows
	}

	// 应用黑白灰风格的表格样式，优化导航体验
	s := table.DefaultStyles()
	s.Header = s.Header.
		BorderStyle(lipgloss.NormalBorder()).
		BorderForeground(colorBorder).
		Foreground(colorPrimary).
		Background(colorSurface).
		BorderBottom(true).
		Bold(true)

	// 关键：根据焦点状态设置选中样式
	if focused {
		s.Selected = s.Selected.
			Foreground(lipgloss.Color("#87CEEB")). // 浅蓝色前景
			Background(lipgloss.Color("#000000")). // 黑色背景
			Bold(true).
			Reverse(true) // 尝试使用反转效果
	} else {
		// 没有焦点时使用默认样式
		s.Selected = s.Selected.
			Foreground(colorText). // 普通文本颜色
			Background(colorBg).   // 黑色背景
			Bold(false)
	}

	s.Cell = s.Cell.
		Foreground(colorText).
		Background(colorBg)

	// 保存当前光标位置
	currentCursor := m.table.Cursor()

	// 重新创建表格
	m.table = table.New(
		table.WithColumns(m.originalCols),
		table.WithRows(m.originalRows),
		table.WithFocused(focused), // 根据参数设置焦点状态
		table.WithHeight(tableHeight),
		table.WithStyles(s),
	)

	// 确保表格能够选择行
	m.table.SetStyles(s)

	// 尝试恢复光标位置（如果表格有足够的行）
	if currentCursor < len(m.originalRows) && len(m.originalRows) > 0 {
		// bubbles table没有直接的SetCursor方法，需要通过Update消息来移动光标
		for i := 0; i < currentCursor; i++ {
			m.table.MoveDown(1)
		}
	}
}

// updateTableDisplay 更新表格显示，根据offset实现水平滚动
func (m *QueryModel) updateTableDisplay() {
	if m.offset < 0 {
		m.offset = 0
	}
	if m.offset >= len(m.originalCols) {
		m.offset = len(m.originalCols) - 1
	}

	visibleColumns := m.originalCols[m.offset:]

	visibleRows := make([]table.Row, 0)
	for _, row := range m.originalRows {
		visibleRow := row[m.offset:]
		visibleRows = append(visibleRows, visibleRow)
	}

	// 智能调整表格高度 - 支持滚动浏览大量数据
	maxDisplayRows := max(10, m.height-15)                    // 为其他UI元素预留空间
	tableHeight := min(len(m.originalRows)+3, maxDisplayRows) // 包含header行

	// 如果数据行数多于显示行数，启用滚动功能
	if len(m.originalRows) > maxDisplayRows-3 {
		tableHeight = maxDisplayRows
	}

	// 应用黑白灰风格的表格样式，优化导航体验
	s := table.DefaultStyles()
	s.Header = s.Header.
		BorderStyle(lipgloss.NormalBorder()).
		BorderForeground(colorBorder).
		Foreground(colorPrimary).
		Background(colorSurface).
		BorderBottom(true).
		Bold(true)

	// 关键：根据当前焦点状态设置选中样式
	if m.focus == focusResultTable {
		s.Selected = s.Selected.
			Foreground(lipgloss.Color("#87CEEB")). // 浅蓝色前景
			Background(lipgloss.Color("#000000")). // 黑色背景
			Bold(true).
			Reverse(true) // 尝试使用反转效果
	} else {
		// 没有焦点时使用默认样式
		s.Selected = s.Selected.
			Foreground(colorText). // 普通文本颜色
			Background(colorBg).   // 黑色背景
			Bold(false)
	}

	s.Cell = s.Cell.
		Foreground(colorText).
		Background(colorBg)

	m.table = table.New(
		table.WithColumns(visibleColumns),
		table.WithRows(visibleRows),
		table.WithFocused(m.focus == focusResultTable), // 根据当前焦点状态设置
		table.WithHeight(tableHeight),
		table.WithStyles(s),
	)
	m.showResults = true // 显示结果在上方
	m.err = nil

	// 确保焦点状态正确
	if m.focus == focusResultTable {
		m.table.Focus()
	} else {
		m.table.Blur()
	}
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// max 辅助函数
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// BubblesQueryTUI Bubbles查询TUI包装器
type BubblesQueryTUI struct {
	model QueryModel
}

// NewBubblesQueryTUI 创建新的Bubbles查询TUI
func NewBubblesQueryTUI(cfg *config.Config) *BubblesQueryTUI {
	return &BubblesQueryTUI{
		model: NewQueryModel(cfg),
	}
}

// Run 运行TUI应用
func (tui *BubblesQueryTUI) Run() error {
	// 不使用 AltScreen，这样不会退出命令行
	p := tea.NewProgram(tui.model)
	_, err := p.Run()
	return err
}
