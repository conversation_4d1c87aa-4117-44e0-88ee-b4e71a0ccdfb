package database

import (
	"database/sql"
	"fmt"

	"dbtool/internal/config"
)

// Driver 数据库驱动接口
type Driver interface {
	Connect(cfg config.DatabaseConfig) (*sql.DB, error)
	Test(cfg config.DatabaseConfig) error
	Query(cfg config.DatabaseConfig, query string) (*sql.Rows, error)
}

// GetDriver 根据驱动名称获取驱动实例
func GetDriver(driverName string) (Driver, error) {
	switch driverName {
	case "mysql":
		return &MySQLDriver{}, nil
	default:
		return nil, fmt.Errorf("不支持的数据库驱动: %s", driverName)
	}
}

// TestConnection 测试数据库连接
func TestConnection(cfg config.DatabaseConfig) error {
	driver, err := GetDriver(cfg.Driver)
	if err != nil {
		return err
	}

	return driver.Test(cfg)
}
