package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"dbtool/internal/config"

	_ "github.com/go-sql-driver/mysql"
)

// MySQLDriver MySQL数据库驱动
type MySQLDriver struct{}

// Connect 连接MySQL数据库
func (m *MySQLDriver) Connect(cfg config.DatabaseConfig) (*sql.DB, error) {
	connectionString := cfg.GetConnectionString()
	if connectionString == "" {
		return nil, fmt.Errorf("无法生成MySQL连接字符串")
	}

	db, err := sql.Open("mysql", connectionString)
	if err != nil {
		return nil, fmt.Errorf("打开MySQL连接失败: %v", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(25)
	db.SetConnMaxLifetime(5 * time.Minute)

	return db, nil
}

// Test 测试MySQL数据库连接
func (m *MySQLDriver) Test(cfg config.DatabaseConfig) error {
	db, err := m.Connect(cfg)
	if err != nil {
		return err
	}
	defer db.Close()

	// 执行ping测试
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		return fmt.Errorf("数据库连接测试失败: %v", err)
	}

	return nil
}

// Query 执行查询
func (m *MySQLDriver) Query(cfg config.DatabaseConfig, query string) (*sql.Rows, error) {
	db, err := m.Connect(cfg)
	if err != nil {
		return nil, err
	}

	rows, err := db.Query(query)
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("查询执行失败: %v", err)
	}

	return rows, nil
}
