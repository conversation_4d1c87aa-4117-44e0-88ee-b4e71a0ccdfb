package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// DatabaseConfig 数据库配置结构
type DatabaseConfig struct {
	Driver   string `yaml:"driver"`
	Source   string `yaml:"source,omitempty"`
	Host     string `yaml:"host,omitempty"`
	Port     int    `yaml:"port,omitempty"`
	DB       string `yaml:"db,omitempty"`
	User     string `yaml:"user,omitempty"`
	Password string `yaml:"password,omitempty"`
	Charset  string `yaml:"charset,omitempty"`
}

// Config 配置文件结构
type Config struct {
	Databases map[string]DatabaseConfig `yaml:",inline"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	config := &Config{}
	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return config, nil
}

// GetDatabase 获取指定名称的数据库配置
func (c *Config) GetDatabase(name string) (DatabaseConfig, bool) {
	db, exists := c.Databases[name]
	return db, exists
}

// GetConnectionString 获取数据库连接字符串
func (db *DatabaseConfig) GetConnectionString() string {
	// 如果直接配置了source，则使用source
	if db.Source != "" {
		return db.Source
	}

	// 否则根据其他字段构建连接字符串
	switch db.Driver {
	case "mysql":
		charset := db.Charset
		if charset == "" {
			charset = "utf8mb4"
		}

		port := db.Port
		if port == 0 {
			port = 3306
		}

		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
			db.User, db.Password, db.Host, port, db.DB, charset)
	default:
		return ""
	}
}

// Validate 验证数据库配置
func (db *DatabaseConfig) Validate() error {
	if db.Driver == "" {
		return fmt.Errorf("数据库驱动不能为空")
	}

	// 如果没有source，则检查其他必需字段
	if db.Source == "" {
		if db.Host == "" {
			return fmt.Errorf("主机地址不能为空")
		}
		if db.User == "" {
			return fmt.Errorf("用户名不能为空")
		}
		if db.DB == "" {
			return fmt.Errorf("数据库名不能为空")
		}
	}

	return nil
}
